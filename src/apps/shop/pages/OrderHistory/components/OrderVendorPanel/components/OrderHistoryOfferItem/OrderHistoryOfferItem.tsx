import dayjs from 'dayjs';
import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { getPriceString } from '@/utils';
import type { OrderHistoryDetailItemType } from '@/libs/orders/types';
import { Tooltip } from '@/libs/ui/Tooltip';
import { t } from 'i18next';
import { getMeasure } from '../../../../utils/getMeasure';
import { OrderStatus } from '../../../OrderStatus/OrderStatus';
import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';
import { FEATURE_FLAGS } from '@/constants';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';

interface OrderHistoryOfferItemProps {
  item: OrderHistoryDetailItemType;
}

export const OrderHistoryOfferItem = ({ item }: OrderHistoryOfferItemProps) => {
  const isFreeItem = item.totalPrice === '0';
  const measure = getMeasure({ item });

  const [shipment] = item.shipments ?? [];
  const dateDelivered =
    item.status === 'DELIVERED' ? shipment?.dateDelivered || null : null;

  const etaDate = [
    'PENDING',
    'PROCESSING',
    'PARTIALLY_SHIPPED',
    'ACCEPTED',
    'SHIPPED',
  ].includes(item.status)
    ? shipment?.etaDateTo || shipment?.etaDateFrom || null
    : null;

  return (
    <div className="grid w-full grid-cols-[110px_1fr] gap-1 pb-4">
      <ProductImage
        product={{ ...item.product, productOfferId: item.productOfferId }}
        className="h-24 w-full"
      />
      <div className="grid w-full grid-cols-[1fr_150px] gap-5">
        <div className="ml-4 flex h-full flex-col justify-between">
          <div>
            <p className="mb-0.5 text-xs font-medium text-gray-600/70">
              Order ID: {item.orderNumber}
            </p>
            {item.product.offers.length > 0 ? (
              <Link
                to={getProductUrl(item.product.id, item.productOfferId)}
                className="cursor-pointer font-medium text-gray-800 no-underline hover:underline"
              >
                {item.product.name}
              </Link>
            ) : (
              <Tooltip label={t('client.orderHistoryItem.notAvailable')}>
                <span
                  tabIndex={0}
                  aria-disabled="true"
                  className="font-medium text-gray-800"
                >
                  {item.product.name}
                </span>
              </Tooltip>
            )}
            {!!measure && (
              <p className="text-xs text-gray-600">UOM: {measure}</p>
            )}
          </div>
          <div className="mt-2 flex items-center text-xs">
            <p className="text-gray-600">
              Quantity:{' '}
              <span className="font-bold text-gray-800">{item.quantity}</span>
            </p>
            <div className="divider-v mx-4" />
            <p className="text-gray-600">
              Price:{' '}
              {isFreeItem ? (
                <span className="font-bold text-green-700">FREE</span>
              ) : (
                <span className="font-bold text-gray-800">
                  {getPriceString(item.unitPrice)}
                </span>
              )}
            </p>
            <div className="divider-v mx-4" />
            <p className="text-gray-600">
              Net Total:{' '}
              {isFreeItem ? (
                <span className="font-bold text-green-700">FREE</span>
              ) : (
                <span className="font-bold text-gray-800">
                  {getPriceString(item.totalPrice)}
                </span>
              )}
            </p>
          </div>
        </div>
        <div className="flex flex-col gap-1 text-right">
          <p className="mb-1 text-xs font-medium text-gray-500/70">Status</p>
          {item.status && (
            <>
              <OrderStatus
                status={item.status}
                align="end"
                trackLink={shipment?.trackingLink ?? ''}
              />

              {dateDelivered ? (
                <p className="text-[10px] text-[#666]">
                  {dayjs(dateDelivered).format('MMMM D, YYYY')}
                </p>
              ) : (
                etaDate && (
                  <p className="flex justify-end gap-1 text-[10px] whitespace-nowrap text-[#666]">
                    Expected Delivery:
                    <span className="font-bold text-[#333]">
                      {dayjs(etaDate).format('MMMM D, YYYY')}
                    </span>
                  </p>
                )
              )}
            </>
          )}
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <Button className="mt-2">
              <Icon name="cartSummary" size={'1.3rem'} />
              <Icon name="plus" size={'0.8rem'} />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
